.registration-header-container, .user-header-container {
  position: relative;
  clip-path: ellipse(200% 100% at 50% 0%);
  overflow: hidden;
  width: 100%;
  --background: transparent;

  .app-header-image {
    padding: 0.9375rem;
    color: var(--ion-text-color-step-950);
    padding-block-end: 2rem;
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(var(--ion-color-primary-rgb), .95) 0%, rgba(var(--ion-color-primary-rgb), 0.85) 100%),
    white;
    z-index: -2;
  }
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("/assets/login-imgs/header-pattern.svg");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    background-position: bottom;
    z-index: -1;
  }

}

.reseller-content, .reseller-footer, .reseller-header {
  --ion-color-primary: var(--reseller-primary);
  --ion-color-primary-rgb: var(--reseller-primary-rgb);
}