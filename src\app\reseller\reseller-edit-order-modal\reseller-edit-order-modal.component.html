<ion-header>
  <ion-toolbar>
    <ion-title>Edit Order</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="closeModal()">
        <ion-icon name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <form [formGroup]="orderForm">
    <div formArrayName="order_lines">
      <ion-card *ngFor="let orderLineControl of orderLines.controls; let i = index" [formGroupName]="i">
        <ion-card-header>
          <ion-card-title>Product {{ i + 1 }}</ion-card-title>
        </ion-card-header>
        
        <ion-card-content>
          <ion-item>
            <ion-label position="stacked">Product Variant</ion-label>
            <ion-input 
              formControlName="product_variant" 
              readonly 
              [value]="getProductVariantName(orderLineControl.get('product_variant')?.value)"
              (click)="selectProduct(orderLineControl, i)">
            </ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">Quantity</ion-label>
            <ion-input formControlName="quantity" type="number" readonly></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">Price</ion-label>
            <ion-input formControlName="price" type="number" readonly></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">Total Price</ion-label>
            <ion-input formControlName="total_price" type="number" readonly></ion-input>
          </ion-item>
        </ion-card-content>
      </ion-card>
    </div>
  </form>
</ion-content>

<ion-footer>
  <ion-toolbar>
    <ion-button 
      expand="block" 
      (click)="saveChanges()" 
      [disabled]="isSaving || !orderForm.valid"
      color="primary">
      <ion-spinner *ngIf="isSaving" name="crescent"></ion-spinner>
      <span *ngIf="!isSaving">Save Changes</span>
    </ion-button>
  </ion-toolbar>
</ion-footer>